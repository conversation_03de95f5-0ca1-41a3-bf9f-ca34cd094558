import { sql } from "@vercel/postgres";
import { PropertyModel } from "@/app/models/Property";
import { JC_ListPagingModel } from "@/app/models/ComponentModels/JC_ListPagingModel";
import { JC_Utils_Business } from "@/app/Utils";

export class PropertyBusiness {

    // - ------ - //
    // - CREATE - //
    // - ------ - //

    static async Create(data: PropertyModel) {
        await sql`
            INSERT INTO public."Property"
            (
                "Id",
                "Address",
                "BuildingTypeCode",
                "CompanyStrataTitleCode",
                "NumBedroomsCode",
                "OrientationCode",
                "StoreysCode",
                "FurnishedCode",
                "OccupiedCode",
                "FloorCode",
                "OtherBuildingElementsCode",
                "OtherTimberBldgElementsCode",
                "RoofCode",
                "WallsCode",
                "WeatherCode",
                "RoomsListJson",
                "SortOrder",
                "CreatedAt"
            )
            VALUES
            (
                ${data.Id},
                ${data.Address},
                ${data.BuildingTypeCode},
                ${data.CompanyStrataTitleCode},
                ${data.NumBedroomsCode},
                ${data.OrientationCode},
                ${data.StoreysCode},
                ${data.FurnishedCode},
                ${data.OccupiedCode},
                ${data.FloorCode},
                ${data.OtherBuildingElementsCode},
                ${data.OtherTimberBldgElementsCode},
                ${data.RoofCode},
                ${data.WallsCode},
                ${data.WeatherCode},
                ${data.RoomsListJson},
                ${data.SortOrder},
                ${new Date().toUTCString()}
            )
        `;
    }

    static async CreateList(dataList: PropertyModel[]) {
        for (const data of dataList) {
            await this.Create(data);
        }
    }

    // - ------ - //
    // - UPDATE - //
    // - ------ - //

    static async Update(data: PropertyModel) {
        await sql`
            UPDATE public."Property"
            SET "Address" = ${data.Address},
                "BuildingTypeCode" = ${data.BuildingTypeCode},
                "CompanyStrataTitleCode" = ${data.CompanyStrataTitleCode},
                "NumBedroomsCode" = ${data.NumBedroomsCode},
                "OrientationCode" = ${data.OrientationCode},
                "StoreysCode" = ${data.StoreysCode},
                "FurnishedCode" = ${data.FurnishedCode},
                "OccupiedCode" = ${data.OccupiedCode},
                "FloorCode" = ${data.FloorCode},
                "OtherBuildingElementsCode" = ${data.OtherBuildingElementsCode},
                "OtherTimberBldgElementsCode" = ${data.OtherTimberBldgElementsCode},
                "RoofCode" = ${data.RoofCode},
                "WallsCode" = ${data.WallsCode},
                "WeatherCode" = ${data.WeatherCode},
                "RoomsListJson" = ${data.RoomsListJson},
                "SortOrder" = ${data.SortOrder},
                "ModifiedAt" = ${new Date().toUTCString()},
                "Deleted" = ${data.Deleted}
            WHERE "Id" = ${data.Id}
        `;
    }

    static async UpdateList(dataList: PropertyModel[]) {
        for (const data of dataList) {
            await this.Update(data);
        }
    }

    static async UpdateSortOrder(id: string, sortOrder: number) {
        await sql`
            UPDATE public."Property"
            SET "SortOrder" = ${sortOrder},
                "ModifiedAt" = ${new Date().toUTCString()}
            WHERE "Id" = ${id}
        `;
    }

    // - ------ - //
    // - DELETE - //
    // - ------ - //

    static async Delete(id: string) {
        await sql`
            UPDATE public."Property"
            SET "Deleted" = 'True',
                "ModifiedAt" = ${new Date().toUTCString()}
            WHERE "Id" = ${id}
        `;
    }

    static async DeleteList(ids: string[]) {
        for (const id of ids) {
            await this.Delete(id);
        }
    }
}
