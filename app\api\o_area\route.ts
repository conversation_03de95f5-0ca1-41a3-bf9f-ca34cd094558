import { NextRequest, NextResponse } from "next/server";
import { unstable_noStore } from "next/cache";
import { O_AreaModel } from "@/app/models/O_Area";
import { O_AreaBusiness } from "./business";

export const dynamic = 'force-dynamic';

// ------- //
// - GET - //
// ------- //

// Get by "Code"
export async function GET(request: NextRequest) {
    try {
        unstable_noStore();
        const params = new URL(request.url).searchParams;
        const code = params.get("code");

        if (!code) {
            return NextResponse.json({ error: "Missing 'code' parameter" }, { status: 400 });
        }

        let result = await O_AreaBusiness.Get(code);
        return NextResponse.json({ result }, { status: 200 });

    } catch (error) {
        console.log(error);
        return NextResponse.json({ error }, { status: 500 });
    }
}

// ---------- //
// - CREATE - //
// ---------- //

export async function PUT(request: NextRequest) {
    try {
        const params = new URL(request.url).searchParams;
        const isList = params.get("list") === "true";

        if (isList) {
            const dataList: O_AreaModel[] = await request.json();
            await O_AreaBusiness.CreateList(dataList);
        } else {
            const data: O_AreaModel = await request.json();
            await O_AreaBusiness.Create(data);
        }

        return NextResponse.json({ status: 200 });

    } catch (error) {
        console.log(error);
        return NextResponse.json({ error }, { status: 500 });
    }
}

// ---------- //
// - UPDATE - //
// ---------- //

export async function POST(request: NextRequest) {
    try {
        const params = new URL(request.url).searchParams;
        const sortOrderOnly = params.get("sortOrder") === "true";

        if (sortOrderOnly) {
            const { code, sortOrder } = await request.json();
            await O_AreaBusiness.UpdateSortOrder(code, sortOrder);
        } else {
            const data: O_AreaModel = await request.json();
            await O_AreaBusiness.Update(data);
        }

        return NextResponse.json({ status: 200 });

    } catch (error) {
        console.log(error);
        return NextResponse.json({ error }, { status: 500 });
    }
}

// ---------- //
// - DELETE - //
// ---------- //

export async function DELETE(request: NextRequest) {
    try {
        const params = new URL(request.url).searchParams;
        const code = params.get("code");
        const isList = params.get("list") === "true";

        if (isList) {
            const codeList: string[] = await request.json();
            await O_AreaBusiness.DeleteList(codeList);
        } else if (code) {
            await O_AreaBusiness.Delete(code);
        } else {
            return NextResponse.json({ error: "Missing 'code' parameter" }, { status: 400 });
        }

        return NextResponse.json({ status: 200 });

    } catch (error) {
        console.log(error);
        return NextResponse.json({ error }, { status: 500 });
    }
}
