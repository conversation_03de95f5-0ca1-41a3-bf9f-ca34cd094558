import { NextRequest, NextResponse } from "next/server";
import { unstable_noStore } from "next/cache";
import { ReportModel } from "@/app/models/Report";
import { ReportBusiness } from "./business";

export const dynamic = 'force-dynamic';

// ------- //
// - GET - //
// ------- //

// Get by "Id", by "UserId", or by "PropertyId"
export async function GET(request: NextRequest) {
    try {
        unstable_noStore();
        const params = new URL(request.url).searchParams;
        const id = params.get("id");
        const userId = params.get("userId");
        const propertyId = params.get("propertyId");

        let result;
        if (userId) {
            result = await ReportBusiness.GetByUserId(userId);
        } else if (propertyId) {
            result = await ReportBusiness.GetByPropertyId(propertyId);
        } else if (id) {
            result = await ReportBusiness.Get(id);
        } else {
            return NextResponse.json({ error: "Missing 'id', 'userId', or 'propertyId' parameter" }, { status: 400 });
        }

        return NextResponse.json({ result }, { status: 200 });

    } catch (error) {
        console.log(error);
        return NextResponse.json({ error }, { status: 500 });
    }
}

// ---------- //
// - CREATE - //
// ---------- //

export async function PUT(request: NextRequest) {
    try {
        const params = new URL(request.url).searchParams;
        const isList = params.get("list") === "true";

        if (isList) {
            const dataList: ReportModel[] = await request.json();
            await ReportBusiness.CreateList(dataList);
        } else {
            const data: ReportModel = await request.json();
            await ReportBusiness.Create(data);
        }

        return NextResponse.json({ status: 200 });

    } catch (error) {
        console.log(error);
        return NextResponse.json({ error }, { status: 500 });
    }
}

// ---------- //
// - UPDATE - //
// ---------- //

export async function POST(request: NextRequest) {
    try {
        const params = new URL(request.url).searchParams;
        const isList = params.get("list") === "true";

        if (isList) {
            const dataList: ReportModel[] = await request.json();
            await ReportBusiness.UpdateList(dataList);
        } else {
            const data: ReportModel = await request.json();
            await ReportBusiness.Update(data);
        }

        return NextResponse.json({ status: 200 });

    } catch (error) {
        console.log(error);
        return NextResponse.json({ error }, { status: 500 });
    }
}

// ---------- //
// - DELETE - //
// ---------- //

export async function DELETE(request: NextRequest) {
    try {
        const params = new URL(request.url).searchParams;
        const id = params.get("id");
        const ids = params.get("ids");

        if (ids) {
            const idList: string[] = ids.split(',');
            await ReportBusiness.DeleteList(idList);
        } else if (id) {
            await ReportBusiness.Delete(id);
        } else {
            return NextResponse.json({ error: "Missing 'id' or 'ids' parameter" }, { status: 400 });
        }

        return NextResponse.json({ status: 200 });

    } catch (error) {
        console.log(error);
        return NextResponse.json({ error }, { status: 500 });
    }
}
