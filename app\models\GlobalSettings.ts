import { JC_Get } from "../apiServices/JC_Get";
import { JC_GetList } from "../apiServices/JC_GetList";
import { JC_Post } from "../apiServices/JC_Post";
import { JC_Put } from "../apiServices/JC_Put";
import { JC_Delete } from "../apiServices/JC_Delete";
import { JC_ListPagingModel } from "./ComponentModels/JC_ListPagingModel";

export class GlobalSettingsModel {

    static tableName: string = "GlobalSettings";
    static apiRoute: string = this.tableName.toLowerCase();
    static defaultSortField: string = "Code";

    // - -------- - //
    // - SERVICES - //
    // - -------- - //
    static async Get(code: string) {
        return await JC_Get<GlobalSettingsModel>(this.apiRoute, { code }, GlobalSettingsModel);
    }
    static async GetList(paging?:JC_ListPagingModel) {
        return await JC_GetList<GlobalSettingsModel>(`${this.apiRoute}/getList`, GlobalSettingsModel, paging, {});
    }
    static async Create(data: GlobalSettingsModel) {
        return await JC_Put<GlobalSettingsModel>(this.apiRoute, data);
    }
    static async CreateList(dataList: GlobalSettingsModel[]) {
        return await JC_Put<GlobalSettingsModel[]>(`${this.apiRoute}/createList`, dataList);
    }
    static async Update(data: GlobalSettingsModel) {
        return await JC_Post<GlobalSettingsModel>(this.apiRoute, data);
    }
    static async UpdateList(dataList: GlobalSettingsModel[]) {
        return await JC_Post<GlobalSettingsModel[]>(`${this.apiRoute}/updateList`, dataList);
    }
    static async Delete(code: string) {
        return await JC_Delete(this.apiRoute, code);
    }
    static async DeleteList(codes: string[]) {
        return await JC_Post(`${this.apiRoute}/deleteList`, { codes });
    }

    // - --------- - //
    // - VARIABLES - //
    // - --------- - //

    Code: string;
    Description: string;
    Value: string;

    // - ----------- - //
    // - CONSTRUCTOR - //
    // - ----------- - //

    constructor(init?: Partial<GlobalSettingsModel>) {
        this.Code = "";
        this.Description = "";
        this.Value = "";
        Object.assign(this, init);
    }

    static getKeys() {
        return Object.keys(new GlobalSettingsModel());
    }

    // - ------ - //
    // - STRING - //
    // - ------ - //

    toString() {
        return `${this.Code} | ${this.Description} | ${this.Value}`;
    }
}
