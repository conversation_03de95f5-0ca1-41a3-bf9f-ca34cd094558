"use client"

import styles from "./page.module.scss";
import { useRouter } from "next/navigation";
import JC_List from "../components/JC_List/JC_List";
import JC_Button from "../components/JC_Button/JC_Button";
import JC_Title from "../components/JC_Title/JC_Title";
import { PropertyModel } from "../models/Property";
import { JC_ListHeader } from "../components/JC_List/JC_ListHeader";
import { LocalStorageKeyEnum } from "../enums/LocalStorageKey";

export default function CustomerPage() {
    const router = useRouter();

    // - HANDLERS - //

    const handleCustomerClick = (customer: PropertyModel) => {
        // Set the selected customer in localStorage
        localStorage.setItem(LocalStorageKeyEnum.JC_SelectedCustomer, customer.Id);
        router.push(`/customer/edit/${customer.Id}`);
    };

    const handleCreateNew = () => {
        router.push('/customer/edit/new');
    };


    // - LIST CONFIGURATION - //

    const listHeaders: JC_ListHeader[] = [
        { sortKey: "Address", label: "Address" }
    ];


    // - BUILD CUSTOMER LIST - //

    function _buildCustomerList() {
        return (
            <div className={styles.propertyListContainer}>
                <div className={styles.createButtonContainer}>
                    <JC_Button
                        text="Create New Customer"
                        onClick={handleCreateNew}
                    />
                </div>

                <JC_List<PropertyModel>
                    service={(paging) => PropertyModel.GetList(paging)}
                    headers={listHeaders}
                    defaultSortKey="Address"
                    defaultSortAsc={true}
                    row={(customer: PropertyModel) => (
                        <tr key={customer.Id} onClick={() => handleCustomerClick(customer)} className={styles.clickableRow}>
                            <td>{customer.Address || 'No address'}</td>
                        </tr>
                    )}
                />
            </div>
        );
    }


    // - RENDER - //

    return (
        <div className={styles.mainContainer}>
            <JC_Title title="Customers" />
            {_buildCustomerList()}
        </div>
    );
}
