import { JC_Get } from "../apiServices/JC_Get";
import { JC_GetList } from "../apiServices/JC_GetList";
import { JC_Post } from "../apiServices/JC_Post";
import { JC_Put } from "../apiServices/JC_Put";
import { JC_Delete } from "../apiServices/JC_Delete";
import { _Base } from "./_Base";
import { JC_ListPagingModel } from "./ComponentModels/JC_ListPagingModel";

export class O_StoreysModel extends _Base {

    static tableName: string = "O_Storeys";
    static apiRoute: string = this.tableName.toLowerCase();
    static defaultSortField: string = "Name";

    // - -------- - //
    // - SERVICES - //
    // - -------- - //
    static async Get(code: string) {
        return await JC_Get<O_StoreysModel>(this.apiRoute, { code }, O_StoreysModel);
    }
    static async GetList(paging?:JC_ListPagingModel) {
        return await JC_GetList<O_StoreysModel>(`${this.apiRoute}/getList`, O_StoreysModel, paging, {});
    }
    static async Create(data: O_StoreysModel) {
        return await JC_Put<O_StoreysModel>(this.apiRoute, data);
    }
    static async CreateList(dataList: O_StoreysModel[]) {
        return await JC_Put<O_StoreysModel[]>(`${this.apiRoute}/createList`, dataList);
    }
    static async Update(data: O_StoreysModel) {
        return await JC_Post<O_StoreysModel>(this.apiRoute, data);
    }
    static async UpdateList(dataList: O_StoreysModel[]) {
        return await JC_Post<O_StoreysModel[]>(`${this.apiRoute}/updateList`, dataList);
    }
    static async Delete(code: string) {
        return await JC_Delete(this.apiRoute, code);
    }
    static async DeleteList(codes: string[]) {
        return await JC_Post(`${this.apiRoute}/deleteList`, { codes });
    }


    // - --------- - //
    // - VARIABLES - //
    // - --------- - //

    Code: string;
    Name: string;
    SortOrder: number;


    // - ----------- - //
    // - CONSTRUCTOR - //
    // - ----------- - //

    constructor(init?: Partial<O_StoreysModel>) {
        super(init);
        this.Code = "";
        this.Name = "";
        this.SortOrder = 999;
        Object.assign(this, init);
    }

    static getKeys() {
        return Object.keys(new O_StoreysModel());
    }


    // - ------ - //
    // - STRING - //
    // - ------ - //

    toString() {
        return `${this.Code} | ${this.Name}`;
    }
}
