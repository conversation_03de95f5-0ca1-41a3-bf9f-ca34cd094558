import { JC_Get } from "../apiServices/JC_Get";
import { JC_GetList } from "../apiServices/JC_GetList";
import { JC_Post } from "../apiServices/JC_Post";
import { JC_Put } from "../apiServices/JC_Put";
import { JC_Delete } from "../apiServices/JC_Delete";
import { _Base } from "./_Base";
import { JC_ListPagingModel } from "./ComponentModels/JC_ListPagingModel";

export class O_CompanyStrataTitleModel extends _Base {

    static tableName: string = "O_CompanyStrataTitle";
    static apiRoute: string = this.tableName.toLowerCase();
    static defaultSortField: string = "Name";

    // - -------- - //
    // - SERVICES - //
    // - -------- - //
    static async Get(code: string) {
        return await JC_Get<O_CompanyStrataTitleModel>(this.apiRoute, { code }, O_CompanyStrataTitleModel);
    }
    static async GetList(paging?:JC_ListPagingModel) {
        return await JC_GetList<O_CompanyStrataTitleModel>(`${this.apiRoute}/getList`, O_CompanyStrataTitleModel, paging, {});
    }
    static async Create(data: O_CompanyStrataTitleModel) {
        return await JC_Put<O_CompanyStrataTitleModel>(this.apiRoute, data);
    }
    static async CreateList(dataList: O_CompanyStrataTitleModel[]) {
        return await JC_Put<O_CompanyStrataTitleModel[]>(`${this.apiRoute}/createList`, dataList);
    }
    static async Update(data: O_CompanyStrataTitleModel) {
        return await JC_Post<O_CompanyStrataTitleModel>(this.apiRoute, data);
    }
    static async UpdateList(dataList: O_CompanyStrataTitleModel[]) {
        return await JC_Post<O_CompanyStrataTitleModel[]>(`${this.apiRoute}/updateList`, dataList);
    }
    static async Delete(code: string) {
        return await JC_Delete(this.apiRoute, code);
    }
    static async DeleteList(codes: string[]) {
        return await JC_Post(`${this.apiRoute}/deleteList`, { codes });
    }

    // - --------- - //
    // - VARIABLES - //
    // - --------- - //

    Code: string;
    Name: string;
    SortOrder: number;

    // - ----------- - //
    // - CONSTRUCTOR - //
    // - ----------- - //

    constructor(init?: Partial<O_CompanyStrataTitleModel>) {
        super(init);
        this.Code = "";
        this.Name = "";
        this.SortOrder = 999;
        Object.assign(this, init);
    }

    static getKeys() {
        return Object.keys(new O_CompanyStrataTitleModel());
    }

    // - ------ - //
    // - STRING - //
    // - ------ - //

    toString() {
        return `${this.Code} | ${this.Name}`;
    }
}
