import { NextRequest, NextResponse } from "next/server";
import { unstable_noStore } from "next/cache";
import { PropertyDefectModel } from "@/app/models/PropertyDefect";
import { PropertyDefectBusiness } from "./business";

export const dynamic = 'force-dynamic';

// ------- //
// - GET - //
// ------- //

// Get by "Id" or by "PropertyId"
export async function GET(request: NextRequest) {
    try {
        unstable_noStore();
        const params = new URL(request.url).searchParams;
        const id = params.get("id");
        const propertyId = params.get("propertyId");

        let result;
        if (propertyId) {
            result = await PropertyDefectBusiness.GetByPropertyId(propertyId);
        } else if (id) {
            result = await PropertyDefectBusiness.Get(id);
        } else {
            return NextResponse.json({ error: "Missing 'id' or 'propertyId' parameter" }, { status: 400 });
        }

        return NextResponse.json({ result }, { status: 200 });

    } catch (error) {
        console.log(error);
        return NextResponse.json({ error }, { status: 500 });
    }
}

// ---------- //
// - CREATE - //
// ---------- //

export async function PUT(request: NextRequest) {
    try {
        const params = new URL(request.url).searchParams;
        const isList = params.get("list") === "true";

        if (isList) {
            const dataList: PropertyDefectModel[] = await request.json();
            await PropertyDefectBusiness.CreateList(dataList);
        } else {
            const data: PropertyDefectModel = await request.json();
            await PropertyDefectBusiness.Create(data);
        }

        return NextResponse.json({ status: 200 });

    } catch (error) {
        console.log(error);
        return NextResponse.json({ error }, { status: 500 });
    }
}

// ---------- //
// - UPDATE - //
// ---------- //

export async function POST(request: NextRequest) {
    try {
        const params = new URL(request.url).searchParams;
        const isList = params.get("list") === "true";

        if (isList) {
            const dataList: PropertyDefectModel[] = await request.json();
            await PropertyDefectBusiness.UpdateList(dataList);
        } else {
            const data: PropertyDefectModel = await request.json();
            await PropertyDefectBusiness.Update(data);
        }

        return NextResponse.json({ status: 200 });

    } catch (error) {
        console.log(error);
        return NextResponse.json({ error }, { status: 500 });
    }
}

// ---------- //
// - DELETE - //
// ---------- //

export async function DELETE(request: NextRequest) {
    try {
        const params = new URL(request.url).searchParams;
        const id = params.get("id");
        const ids = params.get("ids");

        if (ids) {
            const idList: string[] = ids.split(',');
            await PropertyDefectBusiness.DeleteList(idList);
        } else if (id) {
            await PropertyDefectBusiness.Delete(id);
        } else {
            return NextResponse.json({ error: "Missing 'id' or 'ids' parameter" }, { status: 400 });
        }

        return NextResponse.json({ status: 200 });

    } catch (error) {
        console.log(error);
        return NextResponse.json({ error }, { status: 500 });
    }
}
